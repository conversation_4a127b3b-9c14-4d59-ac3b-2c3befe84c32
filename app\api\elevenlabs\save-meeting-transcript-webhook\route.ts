export const runtime = 'nodejs';
export const maxDuration = 60;

import { NextRequest, NextResponse } from 'next/server';
import { saveTranscript, TranscriptMetadata } from '../../../../lib/utils/transcriptUtils';
import { TranscriptMessage } from '../../../../components/PMO/TranscriptPanel';
import { mapAgentTypeToAgenticTeamId, AgenticTeamId } from '../../../../lib/agents/pmo/PMOInterfaces';
import { getAgentConfiguration, updateAgentKnowledgeBase } from '../../../../components/scriptreaderAI/elevenlabs';
import {
  generateMeetingDocument,
  determineMeetingDocumentCategory,
  MeetingDocumentGenerationResult
} from '../../../../lib/tools/meetingDocumentGenerator';

/**
 * Webhook endpoint for ElevenLabs agents to save meeting summaries
 * This endpoint is called by the voice agent when the conversation is ending
 * to save the transcript and optionally generate documents
 *
 * This replaces the need for workspace-level post-call webhooks by allowing
 * the agent to proactively save the meeting content before ending the call.
 */
export async function POST(request: NextRequest) {
  const startTime = Date.now();
  const EARLY_ACK_TIMEOUT = 45000; // 45 seconds - leave buffer for Vercel's 60s limit

  try {
    console.log('[SAVE_MEETING_WEBHOOK] Received save meeting summary request from voice agent');
    console.log('[SAVE_MEETING_WEBHOOK] Request headers:', Object.fromEntries(request.headers.entries()));

    // Verify webhook authentication
    const authHeader = request.headers.get('authorization');
    const expectedAuth = `Bearer ${process.env.ELEVENLABS_WEBHOOK_SECRET || 'pmo-webhook-secret-2024'}`;

    if (authHeader !== expectedAuth) {
      console.error('[SAVE_MEETING_WEBHOOK] Unauthorized webhook request');
      console.error('[SAVE_MEETING_WEBHOOK] Received auth:', authHeader);
      console.error('[SAVE_MEETING_WEBHOOK] Expected auth:', expectedAuth);
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Helper function to check if we should return early
    const shouldReturnEarly = () => {
      const elapsed = Date.now() - startTime;
      return elapsed > EARLY_ACK_TIMEOUT;
    };

    // Parse request body
    const body = await request.json();
    console.log('[SAVE_MEETING_WEBHOOK] Request body:', JSON.stringify(body, null, 2));

    // Extract data from ElevenLabs webhook format
    const {
      summary,
      action_items,
      document_title,
      generate_document = true,
      // Web search results from the meeting
      web_search_results = [],
      search_queries = [],
      research_findings = '',
      // ElevenLabs provides these automatically
      agent_id,
      conversation_id,
      user_id = 'system',
      conversation_history = [],
      meeting_transcript = [],
      transcript = undefined
    } = body as any;

    console.log('[SAVE_MEETING_WEBHOOK] Processing meeting summary:', {
      agent_id,
      conversation_id,
      user_id,
      summary_length: summary?.length || 0,
      has_action_items: !!action_items,
      generate_document,
      conversation_history_length: conversation_history?.length || 0,
      web_search_results_count: web_search_results?.length || 0,
      search_queries_count: search_queries?.length || 0,
      has_research_findings: !!research_findings
    });

    // Prefer full conversation over summaries; accept multiple ElevenLabs shapes
    const rawTranscript: any = (Array.isArray(conversation_history) && conversation_history.length > 0)
      ? conversation_history
      : (Array.isArray(meeting_transcript) && meeting_transcript.length > 0)
        ? meeting_transcript
        : (typeof transcript !== 'undefined' ? transcript : []);

    // Map ElevenLabs transcript to our internal format
    const transcriptMessages: TranscriptMessage[] = mapElevenLabsTranscriptGeneric(rawTranscript);

    if (transcriptMessages.length === 0) {
      console.warn('[SAVE_MEETING_WEBHOOK] No valid transcript content provided (expected conversation_history/meeting_transcript/transcript)');
      return NextResponse.json({
        error: 'Missing full transcript: provide conversation_history or meeting_transcript or transcript',
      }, { status: 400 });
    }

    console.log('[SAVE_MEETING_WEBHOOK] Created transcript with', transcriptMessages.length, 'messages');

    // Resolve agent identity and standardized team mapping
    let agentName = (body.agent_name as string) || 'PMO Agent';
    try {
      if (agent_id) {
        const agentCfg = await getAgentConfiguration(agent_id);
        if (agentCfg?.name && typeof agentCfg.name === 'string') {
          agentName = agentCfg.name;
        }
      }
    } catch (e) {
      console.warn('[SAVE_MEETING_WEBHOOK] Could not fetch ElevenLabs agent configuration, falling back to defaults');
    }

    const derivedTeamId = mapAgentTypeToAgenticTeamId(agentName);
    const agentType = derivedTeamId
      ? ({
          [AgenticTeamId.Marketing]: 'Marketing',
          [AgenticTeamId.Research]: 'Research',
          [AgenticTeamId.SoftwareDesign]: 'SoftwareDesign',
          [AgenticTeamId.Sales]: 'Sales',
          [AgenticTeamId.BusinessAnalysis]: 'BusinessAnalysis',
          [AgenticTeamId.InvestigativeResearch]: 'InvestigativeResearch',
          [AgenticTeamId.CodebaseDocumentation]: 'CodebaseDocumentation',
          [AgenticTeamId.DocumentationGeneration]: 'DocumentationGeneration',
        } as const)[derivedTeamId]
      : (body.agent_type as string) || 'PMO Agent';

    const category = 'Meeting Transcript';
    const finalDocumentTitle = document_title || `Meeting Summary - ${agentName} - ${new Date().toLocaleDateString()}`;

    // Create transcript metadata (now includes standardized agentId)
    const transcriptMetadata: TranscriptMetadata = {
      agentName,
      agentType,
      agentId: derivedTeamId,
      documentTitle: finalDocumentTitle,
      category,
      startTime: new Date(Date.now() - (transcriptMessages.length * 30000)), // Estimate start time
      endTime: new Date(),
      totalMessages: transcriptMessages.length,
      userMessages: transcriptMessages.filter(m => m.role === 'user').length,
      agentMessages: transcriptMessages.filter(m => m.role === 'assistant').length
    };

    console.log('[SAVE_MEETING_WEBHOOK] Saving transcript with metadata:', transcriptMetadata);

    // Save transcript using existing utility with timeout tracking
    console.log('[SAVE_MEETING_WEBHOOK] Starting transcript save process...');
    const saveStartTime = Date.now();

    // Use early-ack pattern: save PDF first, then handle KB upload separately
    const saveResult = await saveTranscript(transcriptMessages, {
      userId: user_id,
      agentId: agent_id || 'unknown',
      metadata: transcriptMetadata,
      uploadToKnowledgeBase: true, // Upload to ElevenLabs KB to match UI flow
      forceUpload: false,
      onProgress: (step: string, message: string, progress?: number) => {
        const elapsed = Date.now() - saveStartTime;
        console.log(`[SAVE_MEETING_WEBHOOK] Progress (${elapsed}ms) - ${step}: ${message} (${progress || 0}%)`);
      }
    });

    const saveElapsed = Date.now() - saveStartTime;
    console.log(`[SAVE_MEETING_WEBHOOK] PDF generation completed in ${saveElapsed}ms`);

    if (!saveResult.success) {
      console.error('[SAVE_MEETING_WEBHOOK] Failed to save transcript:', saveResult.error);
      return NextResponse.json({
        error: 'Failed to save transcript',
        details: saveResult.error
      }, { status: 500 });
    }

    // Check if we should return early to avoid timeout
    if (shouldReturnEarly()) {
      console.log('[SAVE_MEETING_WEBHOOK] Returning early to avoid timeout, KB upload will continue in background');

      // Start background agent KB association (fire and forget)
      if ((agent_id || 'unknown') !== 'unknown') {
        setImmediate(async () => {
          try {
            if (saveResult.knowledgeBaseId) {
              console.log('[SAVE_MEETING_WEBHOOK] Associating document with agent KB in background...', {
                agent_id,
                knowledgeBaseId: saveResult.knowledgeBaseId
              });
              await updateAgentKnowledgeBase(agent_id, saveResult.knowledgeBaseId);
              console.log('[SAVE_MEETING_WEBHOOK] Background agent KB association completed');
            } else {
              console.log('[SAVE_MEETING_WEBHOOK] No knowledgeBaseId returned from save; skipping background KB association');
            }
          } catch (error) {
            console.error('[SAVE_MEETING_WEBHOOK] Background agent KB association failed:', error);
          }
        });
      }

      return NextResponse.json({
        success: true,
        message: 'Meeting summary saved successfully',
        pdfUrl: saveResult.pdfUrl,
        fileName: saveResult.fileName,
        note: 'Knowledge base upload continuing in background'
      });
    }


    console.log('[SAVE_MEETING_WEBHOOK] Transcript saved successfully:', {
      pdfUrl: saveResult.pdfUrl,
      fileName: saveResult.fileName,
      knowledgeBaseId: saveResult.knowledgeBaseId
    });

    // Ensure agent knowledge base is up-to-date (synchronous path)
    if (saveResult.knowledgeBaseId && (agent_id || 'unknown') !== 'unknown') {
      const agentIdForKb = String(agent_id);
      try {
        console.log('[SAVE_MEETING_WEBHOOK] Associating document with agent KB (sync)...', {
          agent_id: agentIdForKb,
          knowledgeBaseId: saveResult.knowledgeBaseId
        });
        await updateAgentKnowledgeBase(agentIdForKb, saveResult.knowledgeBaseId);
        console.log('[SAVE_MEETING_WEBHOOK] Agent KB association completed (sync)');
      } catch (kbErr) {
        console.warn('[SAVE_MEETING_WEBHOOK] Agent KB association failed (sync), continuing:', kbErr);
      }
    }

    // Handle document generation if requested
    let documentGenerationResult: MeetingDocumentGenerationResult | null = null;

    if (generate_document) {
      try {
        console.log('[SAVE_MEETING_WEBHOOK] Document generation requested:', {
          title: finalDocumentTitle,
          summary_length: summary.length
        });

        // Determine appropriate category based on meeting context
        const documentCategory = determineMeetingDocumentCategory(
          [],
          summary + (action_items || '')
        );

        // Generate the meeting document
        documentGenerationResult = await generateMeetingDocument({
          title: finalDocumentTitle,
          category: documentCategory,
          meetingTranscript: transcriptMessages,
          agentId: agent_id || 'unknown',
          userId: user_id,
          webSearchResults: web_search_results, // Include web search results from the meeting
          additionalContext: `Meeting Summary: ${summary}${action_items ? `\n\nAction Items: ${action_items}` : ''}${research_findings ? `\n\nResearch Findings: ${research_findings}` : ''}${search_queries.length > 0 ? `\n\nSearch Queries: ${search_queries.join(', ')}` : ''}`,
          onProgress: (step: string, message: string, progress?: number) => {
            console.log(`[SAVE_MEETING_WEBHOOK] Document Generation - ${step}: ${message} (${progress || 0}%)`);
          }
        });

        if (documentGenerationResult.success) {
          console.log('[SAVE_MEETING_WEBHOOK] Document generated successfully:', {
            documentId: documentGenerationResult.documentId,
            downloadUrl: documentGenerationResult.downloadUrl,
            knowledgeBaseId: documentGenerationResult.knowledgeBaseId
          });
        } else {
          console.error('[SAVE_MEETING_WEBHOOK] Document generation failed:', documentGenerationResult.error);
        }

      } catch (docGenError) {
        console.error('[SAVE_MEETING_WEBHOOK] Error during document generation:', docGenError);
        documentGenerationResult = {
          success: false,
          error: docGenError instanceof Error ? docGenError.message : 'Unknown error during document generation'
        };
      }
    }

    // Return success response to the agent
    const response = {
      success: true,
      message: 'Meeting summary saved successfully',
      results: {
        transcript: {
          saved: true,
          pdfUrl: saveResult.pdfUrl,
          fileName: saveResult.fileName,
          knowledgeBaseId: saveResult.knowledgeBaseId,
          messageCount: transcriptMessages.length
        },
        documentGeneration: documentGenerationResult
      }
    };

    console.log('[SAVE_MEETING_WEBHOOK] Sending success response:', response);
    return NextResponse.json(response);

  } catch (error) {
    console.error('[SAVE_MEETING_WEBHOOK] Error processing save meeting summary webhook:', error);
    return NextResponse.json({
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}


// Map various ElevenLabs transcript shapes to internal TranscriptMessage[]
function mapElevenLabsTranscriptGeneric(transcript: any): TranscriptMessage[] {
  try {
    if (!transcript) return [];

    let messages: any[] = [];

    if (Array.isArray(transcript)) {
      messages = transcript;
    } else if (transcript.messages && Array.isArray(transcript.messages)) {
      messages = transcript.messages;
    } else if (transcript.turns && Array.isArray(transcript.turns)) {
      messages = transcript.turns;
    } else if (typeof transcript === 'string') {
      try {
        const parsed = JSON.parse(transcript);
        if (Array.isArray(parsed)) {
          messages = parsed;
        } else if (parsed?.messages && Array.isArray(parsed.messages)) {
          messages = parsed.messages;
        } else if (parsed?.turns && Array.isArray(parsed.turns)) {
          messages = parsed.turns;
        } else if (parsed?.transcript && Array.isArray(parsed.transcript)) {
          messages = parsed.transcript;
        } else {
          return [{ role: 'assistant', content: transcript, timestamp: new Date() }];
        }
      } catch {
        return [{ role: 'assistant', content: transcript, timestamp: new Date() }];
      }
    } else if (transcript.transcript && Array.isArray(transcript.transcript)) {
      messages = transcript.transcript;
    }

    return messages.map((message: any, index: number) => {
      const role = mapRole(message.role || message.speaker || message.type);
      const content = message.message || message.content || message.text || '';

      let timestamp: Date;
      if (message.time_in_call_secs !== undefined) {
        const estimatedStartTime = Date.now() - (messages.length * 30000);
        timestamp = new Date(estimatedStartTime + (message.time_in_call_secs * 1000));
      } else if (message.timestamp) {
        timestamp = new Date(message.timestamp);
      } else {
        timestamp = new Date(Date.now() - ((messages.length - index) * 30000));
      }

      return { role, content, timestamp };
    }).filter((m: any) => typeof m.content === 'string' && m.content.trim().length > 0);
  } catch (error) {
    console.error('[SAVE_MEETING_WEBHOOK] Error mapping transcript content:', error);
    return [];
  }
}

// Map ElevenLabs roles to internal roles
function mapRole(role: string): string {
  if (!role) return 'assistant';
  const normalized = role.toLowerCase();
  if (normalized.includes('user') || normalized.includes('human') || normalized.includes('customer')) return 'user';
  if (normalized.includes('agent') || normalized.includes('assistant') || normalized.includes('ai')) return 'assistant';
  return 'assistant';
}
