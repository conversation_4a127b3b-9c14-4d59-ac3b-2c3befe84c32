export const runtime = 'nodejs';
export const maxDuration = 60;

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '../../auth/[...nextauth]/authOptions';
import { generatePDF } from '../../../../lib/tools/pdf-generator';
import { mapAgentTypeToAgenticTeamId } from '../../../../lib/agents/pmo/PMOInterfaces';


interface GenerateTranscriptPDFRequest {
  title: string;
  content: string;
  fileName: string;
  category: string;
  metadata: Record<string, string>;
}

export async function POST(request: NextRequest) {
  try {
    // Check authentication - support both session-based and internal API authentication
    let userId: string;

    // Check for internal API authentication first
    const internalAuthHeader = request.headers.get('X-Internal-Auth');
    const internalSecretFromEnv = process.env.INTERNAL_API_SECRET;

    if (internalAuthHeader) {
      // Internal API authentication (server-to-server calls)
      if (!internalSecretFromEnv || internalSecretFromEnv.trim() === '') {
        console.error('[TRANSCRIPT_PDF] INTERNAL_API_SECRET environment variable is not configured properly.');
        return NextResponse.json({
          success: false,
          error: 'Internal authentication is not configured correctly on the server.'
        }, { status: 500 });
      }

      if (internalAuthHeader.trim() === internalSecretFromEnv.trim()) {
        const actingAsUserId = request.headers.get('X-Acting-As-User-Id');
        if (actingAsUserId && actingAsUserId.trim() !== '') {
          userId = actingAsUserId.trim();
          console.log(`[TRANSCRIPT_PDF] Internal authentication successful. Acting as user: ${userId}`);
        } else {
          userId = 'system-webhook-user';
          console.log(`[TRANSCRIPT_PDF] Internal authentication successful. Using system user.`);
        }
      } else {
        console.warn('[TRANSCRIPT_PDF] Internal call attempt with invalid X-Internal-Auth secret.');
        return NextResponse.json({
          success: false,
          error: 'Invalid internal authentication credentials.'
        }, { status: 401 });
      }
    } else {
      // Standard session-based authentication
      const session = await getServerSession(authOptions);
      if (!session?.user?.email) {
        return NextResponse.json({
          success: false,
          error: 'Authentication required'
        }, { status: 401 });
      }
      userId = session.user.email;
    }

    // Parse request body
    const body: GenerateTranscriptPDFRequest = await request.json();
    const { title, content, fileName, category, metadata } = body;

    // Validate required fields
    if (!title || !content || !fileName) {
      return NextResponse.json({
        success: false,
        error: 'Missing required fields: title, content, fileName'
      }, { status: 400 });
    }

    console.log('[TRANSCRIPT_PDF_API] Generating PDF', {
      title,
      fileName,
      category,
      contentLength: content.length,
      userId: userId
    });

    // Resolve AgenticTeamId from metadata.agentType if agentId is missing
    const resolvedAgentId = (metadata && (metadata as any).agentId)
      ? (metadata as any).agentId
      : mapAgentTypeToAgenticTeamId((metadata as any)?.agentType || '') || undefined;

    // Generate PDF using the server-side pdf-generator
    const pdfResult = await generatePDF({
      title,
      content,
      fileName,
      category: category || 'General',
      metadata: {
        ...metadata,
        ...(resolvedAgentId ? { agentId: String(resolvedAgentId) } : {}),
        generatedBy: userId,
        generatedAt: new Date().toISOString(),
        documentType: 'Meeting Transcript'
      }
    });

    console.log('[TRANSCRIPT_PDF_API] PDF generation result', {
      success: !!pdfResult,
      hasFileUrl: !!(pdfResult as any)?.fileUrl,
      userId: userId
    });

    // Check if the result has the expected structure
    if (pdfResult && typeof pdfResult === 'object' && 'success' in pdfResult) {
      const result = pdfResult as any;
      if (result.success) {
        return NextResponse.json({
          success: true,
          fileUrl: result.fileUrl,
          documentId: result.documentId,
          fileName
        });
      } else {
        return NextResponse.json({
          success: false,
          error: result.error || 'PDF generation failed'
        }, { status: 500 });
      }
    }

    // If we get here, the PDF was generated but not saved to storage
    // This shouldn't happen with saveToByteStore: true, but handle it gracefully
    console.warn('[TRANSCRIPT_PDF_API] PDF generated but no storage result returned');
    return NextResponse.json({
      success: false,
      error: 'PDF generation completed but file was not saved to storage'
    }, { status: 500 });

  } catch (error) {
    console.error('[TRANSCRIPT_PDF_API] Error generating PDF:', error);

    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    }, { status: 500 });
  }
}
