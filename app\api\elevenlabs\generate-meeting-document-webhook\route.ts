export const runtime = 'nodejs';
export const maxDuration = 60;

import { NextRequest, NextResponse } from 'next/server';
import {
  generateMeetingDocument,
  determineMeetingDocumentCategory
} from '../../../../lib/tools/meetingDocumentGenerator';
import { TranscriptMessage } from '../../../../components/PMO/TranscriptPanel';

/**
 * Webhook endpoint for ElevenLabs agents to generate documents during meetings
 * This endpoint is called by the voice agent when document generation is requested
 */
export async function POST(request: NextRequest) {
  try {
    console.log('[MEETING_DOC_WEBHOOK] Received document generation request from voice agent');
    console.log('[MEETING_DOC_WEBHOOK] Request headers:', Object.fromEntries(request.headers.entries()));

    // Verify webhook authentication
    const authHeader = request.headers.get('authorization');
    const expectedAuth = `Bearer ${process.env.ELEVENLABS_WEBHOOK_SECRET || 'pmo-webhook-secret-2024'}`;

    // Allow multiple auth tokens for testing and production flexibility
    const validAuthTokens = [
      expectedAuth,
      'Bearer pmo-webhook-secret-2024',
      'Bearer default-secret',
      `Bearer ${process.env.INTERNAL_API_SECRET}`
    ].filter(Boolean);

    if (!authHeader || !validAuthTokens.includes(authHeader)) {
      console.error('[MEETING_DOC_WEBHOOK] Unauthorized webhook request');
      console.error('[MEETING_DOC_WEBHOOK] Received auth:', authHeader);
      console.error('[MEETING_DOC_WEBHOOK] Expected one of:', validAuthTokens);
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Parse request body
    const body = await request.json();
    console.log('[MEETING_DOC_WEBHOOK] Request body:', JSON.stringify(body, null, 2));

    // Extract parameters
    const {
      title,
      category,
      meeting_transcript = [],
      agent_id,
      user_id = 'system',
      web_search_results = [],
      selected_document_categories = [],
      additional_context
    } = body;

    console.log('[MEETING_DOC_WEBHOOK] Extracted parameters:', {
      title,
      category,
      transcriptLength: meeting_transcript.length,
      agent_id,
      user_id,
      webSearchResultsCount: web_search_results.length,
      selectedCategories: selected_document_categories
    });

    // Validate required parameters
    if (!title) {
      console.error('[MEETING_DOC_WEBHOOK] Missing required parameters');
      return NextResponse.json({
        error: 'Missing required parameter: title is required'
      }, { status: 400 });
    }

    // Convert meeting transcript to our internal format if provided
    let transcriptMessages: TranscriptMessage[] = [];
    if (meeting_transcript && Array.isArray(meeting_transcript)) {
      transcriptMessages = meeting_transcript.map((msg: any, index: number) => ({
        role: msg.role || msg.speaker || 'assistant',
        content: msg.content || msg.text || msg.message || '',
        timestamp: msg.timestamp ? new Date(msg.timestamp) : new Date(Date.now() - ((meeting_transcript.length - index) * 30000))
      })).filter((msg: TranscriptMessage) => msg.content.trim().length > 0);
    }

    // Determine document category
    const documentCategory = category || determineMeetingDocumentCategory(
      selected_document_categories,
      transcriptMessages.map(m => m.content).join(' ')
    );

    console.log('[MEETING_DOC_WEBHOOK] Generating document:', {
      title,
      category: documentCategory,
      transcriptLength: transcriptMessages.length,
      hasWebSearchResults: web_search_results.length > 0
    });

    // Generate the meeting document
    const result = await generateMeetingDocument({
      title,
      category: documentCategory,
      meetingTranscript: transcriptMessages,
      agentId: agent_id,
      userId: user_id,
      webSearchResults: web_search_results,
      additionalContext: additional_context,
      onProgress: (step: string, message: string, progress?: number) => {
        console.log(`[MEETING_DOC_WEBHOOK] Progress - ${step}: ${message} (${progress || 0}%)`);
      }
    });

    if (!result.success) {
      console.error('[MEETING_DOC_WEBHOOK] Document generation failed:', result.error);
      return NextResponse.json({
        error: 'Document generation failed',
        details: result.error
      }, { status: 500 });
    }

    console.log('[MEETING_DOC_WEBHOOK] Document generated successfully:', {
      documentId: result.documentId,
      downloadUrl: result.downloadUrl,
      fileName: result.fileName,
      knowledgeBaseId: result.knowledgeBaseId
    });

    // Return success response with document details
    return NextResponse.json({
      success: true,
      message: 'Document generated successfully',
      document: {
        id: result.documentId,
        title,
        category: documentCategory,
        downloadUrl: result.downloadUrl,
        fileName: result.fileName,
        knowledgeBaseId: result.knowledgeBaseId
      },
      summary: `I've successfully created a document titled "${title}" in the ${documentCategory} category. The document has been saved to your PMO documents collection and uploaded to my knowledge base for future reference.`
    });

  } catch (error) {
    console.error('[MEETING_DOC_WEBHOOK] Error processing document generation webhook:', error);
    return NextResponse.json({
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
